#!/usr/bin/env python3
"""
Manual script to reformat HTML tables - simpler approach
"""

import re
from pathlib import Path

def reformat_table(content):
    """
    Manually reformat the HTML table structure
    """
    lines = content.split('\n')
    new_lines = []
    
    skip_next = False
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        if skip_next:
            skip_next = False
            i += 1
            continue
            
        # Replace header row
        if 'Estimates' in line and 'std. Error' in line:
            # Replace with model numbers
            new_header = re.sub(
                r'<td[^>]*>Estimates</td>\s*<td[^>]*>std\. Error</td>',
                '<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">(1)</td>',
                line, count=1
            )
            new_header = re.sub(
                r'<td[^>]*>Estimates</td>\s*<td[^>]*>std\. Error</td>',
                '<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">(2)</td>',
                new_header, count=1
            )
            new_header = re.sub(
                r'<td[^>]*>Estimates</td>\s*<td[^>]*>std\. Error</td>',
                '<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">(3)</td>',
                new_header, count=1
            )
            new_header = re.sub(
                r'<td[^>]*>Estimates</td>\s*<td[^>]*>std\. Error</td>',
                '<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">(4)</td>',
                new_header, count=1
            )
            new_lines.append(new_header)
            
        # Process data rows
        elif '<tr>' in line and i + 1 < len(lines):
            next_line = lines[i + 1]
            
            # Check if this is a data row with coefficients
            if (re.search(r'<td[^>]*>[^<]*<sup>', next_line) or 
                re.search(r'<td[^>]*>&#45;\d+\.\d+', next_line) or
                re.search(r'<td[^>]*>\d+\.\d+', next_line)):
                
                # Extract variable name
                var_match = re.search(r'<td[^>]*>([^<]+)</td>', next_line)
                if var_match:
                    var_name = var_match.group(1)
                    
                    # Extract all cell contents
                    cells = re.findall(r'<td[^>]*>([^<]*(?:<sup>[^<]*</sup>)?[^<]*)</td>', next_line)
                    
                    if len(cells) > 1:
                        # Create coefficient row
                        coef_row = '<tr>\n'
                        coef_row += f'<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">{var_name}</td>\n'
                        
                        # Create std error row
                        std_row = '<tr>\n'
                        std_row += '<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; "></td>\n'
                        
                        # Process coefficient/std error pairs (skip first cell which is variable name)
                        data_cells = cells[1:]
                        for j in range(0, len(data_cells), 2):
                            if j + 1 < len(data_cells):
                                coef = data_cells[j]
                                std_err = data_cells[j + 1]
                                
                                # Add coefficient
                                coef_row += f'<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">{coef}</td>\n'
                                
                                # Add std error with parentheses
                                if std_err.strip():
                                    std_row += f'<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">({std_err})</td>\n'
                                else:
                                    std_row += '<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>\n'
                        
                        coef_row += '</tr>'
                        std_row += '</tr>'
                        
                        new_lines.append(line)  # <tr>
                        new_lines.append(coef_row)
                        new_lines.append(std_row)
                        
                        # Skip the original data row and closing </tr>
                        i += 2
                        continue
        
        new_lines.append(line)
        i += 1
    
    return '\n'.join(new_lines)

def process_file(file_path):
    """Process a single HTML file"""
    print(f"Processing {file_path}")
    
    # Read original content
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Reformat
    new_content = reformat_table(content)
    
    # Write back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Completed {file_path}")

def main():
    """Main function"""
    html_files = list(Path('.').glob('*.html'))
    
    for html_file in html_files:
        try:
            process_file(html_file)
        except Exception as e:
            print(f"Error processing {html_file}: {e}")

if __name__ == "__main__":
    main()
