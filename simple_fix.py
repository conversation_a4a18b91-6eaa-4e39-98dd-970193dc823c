#!/usr/bin/env python3
"""
简单的HTML表格修复脚本
"""

import os
import re

def fix_html_file(filename):
    print(f"修复文件: {filename}")
    
    # 读取文件
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建备份
    backup_name = filename + '.backup2'
    with open(backup_name, 'w', encoding='utf-8') as f:
        f.write(content)
    
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # 修复表头的colspan
        if '<th' in line and 'colspan="0"' in line:
            # 根据文件名判断应该的列数
            if 'B2_' in filename or 'B4_' in filename or 'B5_' in filename:
                line = line.replace('colspan="0"', 'colspan="3"')
            elif 'B3_' in filename:
                line = line.replace('colspan="0"', 'colspan="4"')
            elif 'B6_' in filename or 'B7_' in filename:
                line = line.replace('colspan="0"', 'colspan="3"')
        
        # 修复原始的colspan="2"
        if '<th' in line and 'colspan="2"' in line:
            if 'B3_' in filename:
                line = line.replace('colspan="2"', 'colspan="4"')
            else:
                line = line.replace('colspan="2"', 'colspan="3"')
        
        # 修复统计部分的colspan
        if '<td' in line and 'colspan=' in line:
            if any(stat in line for stat in ['σ', 'τ', 'ICC', 'Observations', 'Marginal R', 'AIC', 'log-Likelihood']):
                if 'B3_' in filename:
                    line = re.sub(r'colspan="\d+"', 'colspan="4"', line)
                else:
                    line = re.sub(r'colspan="\d+"', 'colspan="3"', line)
        
        # 修复Random Effects行
        if 'Random Effects' in line:
            if 'B3_' in filename:
                line = re.sub(r'colspan="\d+"', 'colspan="5"', line)
            else:
                line = re.sub(r'colspan="\d+"', 'colspan="4"', line)
        
        fixed_lines.append(line)
        
        # 添加缺失的</tr>标签
        if i < len(lines) - 1:
            current_line = line.strip()
            next_line = lines[i + 1].strip() if i + 1 < len(lines) else ""
            
            # 如果当前行有<td>且下一行是新的<tr>或</table>，需要添加</tr>
            if ('<td' in current_line or '<th' in current_line) and current_line != "":
                if (next_line.startswith('<tr>') or 
                    next_line.startswith('</table>') or 
                    (i + 1 == len(lines) - 1)):
                    # 检查是否已经有</tr>
                    has_closing = False
                    for j in range(i + 1, min(i + 5, len(lines))):
                        if '</tr>' in lines[j]:
                            has_closing = True
                            break
                        if '<tr>' in lines[j] or '</table>' in lines[j]:
                            break
                    
                    if not has_closing and '</tr>' not in current_line:
                        fixed_lines.append('</tr>')
    
    # 写入修复后的内容
    with open(filename, 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines))
    
    print(f"修复完成: {filename}")

def main():
    html_files = [f for f in os.listdir('.') if f.endswith('.html')]
    
    for html_file in html_files:
        try:
            fix_html_file(html_file)
        except Exception as e:
            print(f"修复 {html_file} 时出错: {e}")

if __name__ == "__main__":
    main()
