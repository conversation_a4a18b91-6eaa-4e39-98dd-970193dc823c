#!/usr/bin/env python3
"""
验证HTML表格修复情况的脚本
"""

import os
import re

def verify_table_structure(filename):
    print(f"\n验证文件: {filename}")
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    issues = []
    
    # 检查表头colspan
    for i, line in enumerate(lines):
        if '<th' in line and 'colspan=' in line:
            colspan_match = re.search(r'colspan="(\d+)"', line)
            if colspan_match:
                colspan = int(colspan_match.group(1))
                print(f"  表头colspan: {colspan}")
                
                # 检查数据行的列数
                data_cols = 0
                for j in range(i + 1, min(i + 10, len(lines))):
                    if 'Predictors' in lines[j]:
                        # 计算这一行的td数量
                        for k in range(j, min(j + 5, len(lines))):
                            if '<td' in lines[k]:
                                data_cols = max(data_cols, lines[k].count('<td'))
                        break
                
                print(f"  数据列数: {data_cols}")
                if colspan != data_cols - 1:
                    issues.append(f"表头colspan({colspan})与数据列数({data_cols})不匹配")
    
    # 检查Random Effects行
    random_effects_found = False
    for i, line in enumerate(lines):
        if 'Random Effects' in line:
            random_effects_found = True
            colspan_match = re.search(r'colspan="(\d+)"', line)
            if colspan_match:
                colspan = int(colspan_match.group(1))
                print(f"  Random Effects colspan: {colspan}")
            else:
                issues.append("Random Effects行缺少colspan属性")
    
    if not random_effects_found:
        issues.append("未找到Random Effects行")
    
    # 检查统计数据行是否还有错误的colspan
    stats_with_colspan = []
    for i, line in enumerate(lines):
        if any(stat in line for stat in ['σ', 'τ', 'ICC', 'Observations', 'Marginal R', 'AIC', 'log-Likelihood']):
            if 'colspan=' in line and 'Random Effects' not in line:
                stats_with_colspan.append(f"第{i+1}行: {line.strip()[:50]}...")
    
    if stats_with_colspan:
        issues.append(f"发现{len(stats_with_colspan)}个统计数据行仍有colspan属性")
        for stat in stats_with_colspan[:3]:  # 只显示前3个
            print(f"    {stat}")
    
    # 检查缺失的</tr>标签
    tr_balance = 0
    for line in lines:
        tr_balance += line.count('<tr>')
        tr_balance -= line.count('</tr>')
    
    if tr_balance != 0:
        issues.append(f"<tr>和</tr>标签不平衡，差值: {tr_balance}")
    
    if issues:
        print(f"  发现问题:")
        for issue in issues:
            print(f"    - {issue}")
    else:
        print(f"  ✓ 表格结构正确")
    
    return len(issues) == 0

def main():
    html_files = [f for f in os.listdir('.') if f.endswith('.html')]
    html_files.sort()
    
    print("验证所有HTML表格结构...")
    
    all_good = True
    for html_file in html_files:
        is_good = verify_table_structure(html_file)
        all_good = all_good and is_good
    
    print(f"\n{'='*50}")
    if all_good:
        print("✓ 所有表格结构验证通过！")
    else:
        print("✗ 部分表格仍有问题，需要进一步修复")

if __name__ == "__main__":
    main()
