<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<style>body{background-color:white;}</style>


</head>
<body>
<div id="xxfeqzkqym" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;">
  <style>#xxfeqzkqym table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#xxfeqzkqym thead, #xxfeqzkqym tbody, #xxfeqzkqym tfoot, #xxfeqzkqym tr, #xxfeqzkqym td, #xxfeqzkqym th {
  border-style: none;
}

#xxfeqzkqym p {
  margin: 0;
  padding: 0;
}

#xxfeqzkqym .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 11px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#xxfeqzkqym .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#xxfeqzkqym .gt_title {
  color: #333333;
  font-size: 12px;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#xxfeqzkqym .gt_subtitle {
  color: #333333;
  font-size: 11px;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#xxfeqzkqym .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#xxfeqzkqym .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#xxfeqzkqym .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#xxfeqzkqym .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#xxfeqzkqym .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#xxfeqzkqym .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#xxfeqzkqym .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#xxfeqzkqym .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#xxfeqzkqym .gt_spanner_row {
  border-bottom-style: hidden;
}

#xxfeqzkqym .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#xxfeqzkqym .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#xxfeqzkqym .gt_from_md > :first-child {
  margin-top: 0;
}

#xxfeqzkqym .gt_from_md > :last-child {
  margin-bottom: 0;
}

#xxfeqzkqym .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#xxfeqzkqym .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#xxfeqzkqym .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#xxfeqzkqym .gt_row_group_first td {
  border-top-width: 2px;
}

#xxfeqzkqym .gt_row_group_first th {
  border-top-width: 2px;
}

#xxfeqzkqym .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#xxfeqzkqym .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#xxfeqzkqym .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#xxfeqzkqym .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#xxfeqzkqym .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#xxfeqzkqym .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#xxfeqzkqym .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#xxfeqzkqym .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#xxfeqzkqym .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#xxfeqzkqym .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#xxfeqzkqym .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#xxfeqzkqym .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#xxfeqzkqym .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#xxfeqzkqym .gt_left {
  text-align: left;
}

#xxfeqzkqym .gt_center {
  text-align: center;
}

#xxfeqzkqym .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#xxfeqzkqym .gt_font_normal {
  font-weight: normal;
}

#xxfeqzkqym .gt_font_bold {
  font-weight: bold;
}

#xxfeqzkqym .gt_font_italic {
  font-style: italic;
}

#xxfeqzkqym .gt_super {
  font-size: 65%;
}

#xxfeqzkqym .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#xxfeqzkqym .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#xxfeqzkqym .gt_indent_1 {
  text-indent: 5px;
}

#xxfeqzkqym .gt_indent_2 {
  text-indent: 10px;
}

#xxfeqzkqym .gt_indent_3 {
  text-indent: 15px;
}

#xxfeqzkqym .gt_indent_4 {
  text-indent: 20px;
}

#xxfeqzkqym .gt_indent_5 {
  text-indent: 25px;
}

#xxfeqzkqym .katex-display {
  display: inline-flex !important;
  margin-bottom: 0.75em !important;
}

#xxfeqzkqym div.Reactable > div.rt-table > div.rt-thead > div.rt-tr.rt-tr-group-header > div.rt-th-group:after {
  height: 0px !important;
}
</style>
  <table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false">
  <!--/html_preserve--><caption class='gt_caption'>Table B6. Fixed Effects Models: Political Connections</caption><!--html_preserve-->
  <thead>
    <tr class="gt_col_headings">
      <th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="a-"> </th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Two-way-FE">Two-way FE</th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Individual-FE">Individual FE</th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Interaction-Model">Interaction Model</th>
    </tr>
  </thead>
  <tbody class="gt_table_body">
    <tr><td headers=" " class="gt_row gt_left">Connections</td>
<td headers="Two-way FE" class="gt_row gt_center">-0.175***</td>
<td headers="Individual FE" class="gt_row gt_center"></td>
<td headers="Interaction Model" class="gt_row gt_center">-0.300***</td></tr>
    <tr><td headers=" " class="gt_row gt_left"></td>
<td headers="Two-way FE" class="gt_row gt_center">(0.057)</td>
<td headers="Individual FE" class="gt_row gt_center"></td>
<td headers="Interaction Model" class="gt_row gt_center">(0.071)</td></tr>
    <tr><td headers=" " class="gt_row gt_left">After First Inspection</td>
<td headers="Two-way FE" class="gt_row gt_center"></td>
<td headers="Individual FE" class="gt_row gt_center">6.799***</td>
<td headers="Interaction Model" class="gt_row gt_center">6.040***</td></tr>
    <tr><td headers=" " class="gt_row gt_left"></td>
<td headers="Two-way FE" class="gt_row gt_center"></td>
<td headers="Individual FE" class="gt_row gt_center">(0.200)</td>
<td headers="Interaction Model" class="gt_row gt_center">(0.252)</td></tr>
    <tr><td headers=" " class="gt_row gt_left">ESG Rating</td>
<td headers="Two-way FE" class="gt_row gt_center">0.151***</td>
<td headers="Individual FE" class="gt_row gt_center">0.216***</td>
<td headers="Interaction Model" class="gt_row gt_center">0.217***</td></tr>
    <tr><td headers=" " class="gt_row gt_left"></td>
<td headers="Two-way FE" class="gt_row gt_center">(0.016)</td>
<td headers="Individual FE" class="gt_row gt_center">(0.017)</td>
<td headers="Interaction Model" class="gt_row gt_center">(0.017)</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Return on Assets</td>
<td headers="Two-way FE" class="gt_row gt_center">-0.374***</td>
<td headers="Individual FE" class="gt_row gt_center">-0.332***</td>
<td headers="Interaction Model" class="gt_row gt_center">-0.326***</td></tr>
    <tr><td headers=" " class="gt_row gt_left"></td>
<td headers="Two-way FE" class="gt_row gt_center">(0.088)</td>
<td headers="Individual FE" class="gt_row gt_center">(0.096)</td>
<td headers="Interaction Model" class="gt_row gt_center">(0.096)</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Leverage</td>
<td headers="Two-way FE" class="gt_row gt_center">-0.002</td>
<td headers="Individual FE" class="gt_row gt_center">-0.001</td>
<td headers="Interaction Model" class="gt_row gt_center">-0.001</td></tr>
    <tr><td headers=" " class="gt_row gt_left"></td>
<td headers="Two-way FE" class="gt_row gt_center">(0.006)</td>
<td headers="Individual FE" class="gt_row gt_center">(0.007)</td>
<td headers="Interaction Model" class="gt_row gt_center">(0.007)</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Log(Registered Capital)</td>
<td headers="Two-way FE" class="gt_row gt_center">0.557***</td>
<td headers="Individual FE" class="gt_row gt_center">2.165***</td>
<td headers="Interaction Model" class="gt_row gt_center">2.223***</td></tr>
    <tr><td headers=" " class="gt_row gt_left" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"></td>
<td headers="Two-way FE" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;">(0.190)</td>
<td headers="Individual FE" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;">(0.198)</td>
<td headers="Interaction Model" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;">(0.198)</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Num.Obs.</td>
<td headers="Two-way FE" class="gt_row gt_center">10771</td>
<td headers="Individual FE" class="gt_row gt_center">10777</td>
<td headers="Interaction Model" class="gt_row gt_center">10771</td></tr>
    <tr><td headers=" " class="gt_row gt_left">R²</td>
<td headers="Two-way FE" class="gt_row gt_center">0.033</td>
<td headers="Individual FE" class="gt_row gt_center">0.232</td>
<td headers="Interaction Model" class="gt_row gt_center">0.234</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Adj.R²</td>
<td headers="Two-way FE" class="gt_row gt_center">-0.129</td>
<td headers="Individual FE" class="gt_row gt_center">0.104</td>
<td headers="Interaction Model" class="gt_row gt_center">0.107</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Firm fixed effects</td>
<td headers="Two-way FE" class="gt_row gt_center">Y</td>
<td headers="Individual FE" class="gt_row gt_center">Y</td>
<td headers="Interaction Model" class="gt_row gt_center">Y</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Industry fixed effects</td>
<td headers="Two-way FE" class="gt_row gt_center">Y</td>
<td headers="Individual FE" class="gt_row gt_center">Y</td>
<td headers="Interaction Model" class="gt_row gt_center">Y</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Province fixed effects</td>
<td headers="Two-way FE" class="gt_row gt_center">Y</td>
<td headers="Individual FE" class="gt_row gt_center">Y</td>
<td headers="Interaction Model" class="gt_row gt_center">Y</td></tr>
    <tr><td headers=" " class="gt_row gt_left">Year fixed effects</td>
<td headers="Two-way FE" class="gt_row gt_center">Y</td>
<td headers="Individual FE" class="gt_row gt_center">N</td>
<td headers="Interaction Model" class="gt_row gt_center">N</td></tr>
</tr>
  </tbody>
  <tfoot class="gt_sourcenotes">
    <tr>
      <td class="gt_sourcenote" colspan="4">Standard errors in parentheses.</td>
    </tr>
    <tr>
      <td class="gt_sourcenote" colspan="4">* p &lt; 0.1, ** p &lt; 0.05, *** p &lt; 0.01</td>
    </tr>
  </tfoot>
  
</table>
</div>
</body>
</html>
